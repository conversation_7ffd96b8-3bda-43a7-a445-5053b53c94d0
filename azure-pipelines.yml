trigger:
  branches:
    include:
    - uat

pool:
  vmImage: 'ubuntu-latest'

variables:
  containerRegistryUrl: 'cdss3uatacr.azurecr.io'
  imageRepository: 'ctint-digital-wechat-connector'
  deployment: 'ctint-digital-wechat-connector'
  tag: '1.0.$(Build.BuildId)'
  kubernetesServiceConnection: 'uat_aks_service_ctint_digital_wechat_connector'
  environment: 'uat_ask_ctint_digital_wechat_connector'

jobs:
- job: BuildAndPushToACR
  displayName: Build and push to ACR
  steps:
  - task: Docker@2
    inputs:
      containerRegistry: 'cdss3uatacr'
      repository: '$(imageRepository)'
      command: 'buildAndPush'
      Dockerfile: '**/Dockerfile'
      tags: '$(tag)'

- deployment: DeployToK8s  # Change 'job' to 'deployment'
  displayName: Deploy to AKS
  dependsOn: BuildAndPushToACR
  environment: $(environment)  # Environment is valid in deployment jobs
  strategy:
    runOnce:
      deploy:
        steps:
          - checkout: self

          - script: |
              echo "Updating deployment.yml with tag $(tag)"
              sed -i 's|image: .*|image: $(containerRegistryUrl)/$(imageRepository):$(tag)|' ./k8s/$(deployment)-deployment-uat.yaml
            displayName: Update deployment.yml with the new version

          - task: KubernetesManifest@0
            displayName: Deploy to Kubernetes cluster
            inputs:
              action: deploy
              manifests: |
                ./k8s/$(deployment)-deployment-uat.yaml
              kubernetesServiceConnection: $(kubernetesServiceConnection)
