apiVersion: apps/v1
kind: Deployment
metadata:
  name: ctint-digital-wechat-connector-app-deployment
  namespace: ctint-digital-wechat-connector
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ctint-digital-wechat-connector-app-deployment
  template:
    metadata:
      labels:
        app: ctint-digital-wechat-connector-app-deployment
    spec:
      containers:
      - name: ctint-digital-wechat-connector-app
        image: cdss3uatacr.azurecr.io/ctint-digital-wechat-connector:1.0.6802
        imagePullPolicy: Always
        args: ["uat", '{"ipAddr":"digitaluat.eastasia.cloudapp.azure.com","port":80,"contextPath":"/ctint/nacos","namespaceId":"12962050-a1b8-4b0e-9893-bf797df76261","timeout":5000,"username":"nacos","password":"","dataId":"digital_wechat_connector_uat","group":"wechat_connector","withoutLoadCache":true}']
        env:
          - name: nacospw  # 您要设置的环境变量名
            valueFrom:
              secretKeyRef:
                name: ctintcdss3uatakssa-share  # Secret的名称
                key: nacospw  # Secret中的键名
        resources:
          requests:
            cpu: 50m
            memory: 100Mi
          limits:
            cpu: 500m
            memory: 512Mi
#        livenessProbe:
#          httpGet:
#            path: /digital-wechat-connector/api/v1/index/
#            port: 8203
#          periodSeconds: 60
#          failureThreshold: 3
        ports:
        - containerPort: 8208
        volumeMounts:
        - name: ctint-digital-wechat-connector-logs
          mountPath: /app/logs
        # livenessProbe:
        #   httpGet:
        #     path: /ctint-digital-connector/healthcheck
        #     port: 8090
        #   periodSeconds: 300
        #   failureThreshold: 3
      volumes:
      - name: ctint-digital-wechat-connector-logs
        azureFile:
          secretName: ctintcdss3uatakssa-share
          shareName: ctint-cdss-logs/ctint-digital-wechat-connector
          readOnly: false