apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: ctint-digital-wechat-connector-hpa
  namespace: ctint-digital-wechat-connector
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: ctint-digital-wechat-connector-app-deployment
  minReplicas: 2  # 最少2个pod
  maxReplicas: 5 # 最多10个pod
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 80  # 当CPU使用率超过80%时开始扩容
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80  # 当内存使用率超过80%时开始扩容
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 300  # 缩容前等待5分钟稳定期
      policies:
      - type: Percent
        value: 50   # 每次最多缩容50%
        periodSeconds: 60
    scaleUp:
      stabilizationWindowSeconds: 60   # 扩容前等待1分钟稳定期
      policies:
      - type: Percent
        value: 100  # 每次最多扩容100%（翻倍）
        periodSeconds: 60 