apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ctint-digital-wechat-connector-app-ingress
  namespace: ctint-digital-wechat-connector
  annotations:
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /ctint-digital-wechat-connector/$2
spec:
  ingressClassName: "nginx"
  rules:
  - http:
      paths:
      - path: /ctint/digital-wechat-connector(/|$)(.*)
        pathType: ImplementationSpecific
        backend:
          service:
            name: ctint-digital-wechat-connector-app-service
            port:
              number: 8208