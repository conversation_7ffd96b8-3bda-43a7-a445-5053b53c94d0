FROM golang:1.22 AS build
WORKDIR /app

COPY ./src ./src
COPY ./go.mod ./go.mod
COPY ./go.sum ./go.sum
RUN go env -w GO111MODULE=on
RUN go env -w GOPROXY=https://goproxy.io,direct

RUN go env -w GOPRIVATE=dev.azure.com
RUN git config --global url."https://Kelvin:<EMAIL>".insteadOf "https://dev.azure.com"

RUN rm -rf $(go env GOPATH)/pkg/mod
#RUN go build -o ./bin/ctint-digital-connector-app ./src
RUN go mod download && go build -ldflags="-s -w" -o ./bin/ctint-digital-wechat-connector ./src

FROM ubuntu:22.04
WORKDIR /app

RUN mkdir logs

RUN apt-get update && apt-get install -y \
    net-tools \
    curl \
    telnet \
    iputils-ping \
    traceroute \
    dnsutils \
    iproute2 \
    && rm -rf /var/lib/apt/lists/*

#COPY ./global-config ./global-config
#COPY ./microservices-config ./microservices-config

COPY --from=build /app/bin/ctint-digital-wechat-connector /app/bin/ctint-digital-wechat-connector


WORKDIR /app/bin
EXPOSE 8208

ENTRYPOINT [ "./ctint-digital-wechat-connector"]