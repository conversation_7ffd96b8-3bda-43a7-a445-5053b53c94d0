trigger: none
pool:
  vmImage: 'ubuntu-latest'

variables:
  #  containerRegistry: 'cdss3uatacr'  # ACR名称
  containerRegistryDisplay: '$(containerRegistry) '
  #  repositoryName: 'ctint-digital-externalapi'
  imageVersion: ''
  imageName: '$(imagePrefix)-digital-wechat-connector'

steps:
  - script: |
      tagName=$(echo $(Build.SourceBranchName) | sed 's/[/_]/-/g')
      echo "处理后的标签名称: $tagName"
      echo "##vso[task.setvariable variable=imageVersion]$tagName"
    displayName: '处理标签名称'

  - task: Docker@2
    inputs:
      containerRegistry: '$(containerRegistry)'
      repository: '$(imageName)'
      command: 'buildAndPush'
      Dockerfile: '**/Dockerfile'
      tags: '$(imageVersion)'
    displayName: '构建并推送镜像'

  - script: |
      echo "完整镜像地址：$(containerRegistryDisplay).azurecr.io/$(imageName):$(imageVersion)"
    displayName: '显示镜像信息'