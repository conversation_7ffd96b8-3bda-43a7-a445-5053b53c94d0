package auth

import (
	"context"
	commonConfig "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/model/config"
	"net/http"
)

type storeKey string

var channelConfigKey = storeKey("channelConfigKey")
var currentEntryPlatform = storeKey("currentEntryPlatform")

/*Channel*/
func SaveChannelConfig(ctx *context.Context, u commonConfig.Channel) {
	SaveContextValue[commonConfig.Channel](ctx, channelConfigKey, u)
}
func SaveChannelConfigR(r *http.Request, u commonConfig.Channel) *http.Request {
	ctx := r.Context()
	SaveContextValue[commonConfig.Channel](&ctx, channelConfigKey, u)
	return r.WithContext(ctx)
}

func GetChannelConfig(ctx context.Context) *commonConfig.Channel {
	result, ok := GetContextValue[commonConfig.Channel](ctx, channelConfigKey)
	if !ok {
		return nil
	}
	return &result
}

/*JWT*/
func SaveCurrentEntryPlatform(ctx *context.Context, u string) {
	SaveContextValue[string](ctx, currentEntryPlatform, u)
}
func SaveCurrentEntryPlatformR(r *http.Request, u string) *http.Request {
	ctx := r.Context()
	SaveContextValue[string](&ctx, currentEntryPlatform, u)
	return r.WithContext(ctx)
}
func GetCurrentEntryPlatform(ctx context.Context) *string {
	result, ok := GetContextValue[string](ctx, currentEntryPlatform)
	if !ok {
		return nil
	}
	return &result
}

/*Basic*/
func SaveContextValue[T any](ctx *context.Context, key storeKey, value T) {
	nCtx := context.WithValue(*ctx, key, value)
	*ctx = nCtx
}
func GetContextValue[T any](ctx context.Context, key storeKey) (T, bool) {
	u, ok := ctx.Value(key).(T)
	return u, ok
}
