package auth

import (
	"ctint-digital-wechat-connector/src/pkg/cthttp"
	"ctint-digital-wechat-connector/src/pkg/nacos"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"github.com/google/uuid"
	"net/http"
	"strings"

	errors "github.com/pkg/errors"

	commonLogger "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/log"
	commonConfig "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/model/config"

	"github.com/dgrijalva/jwt-go"
)

type JWTPlatform string

const (
	JWTPlatformWhatsApp     JWTPlatform = "whatsapp"
	JWTPlatformGenesysCloud JWTPlatform = "genesys-cloud"
	JWTPlatformEmail        JWTPlatform = "email"
	JWTPlatformUndefined    JWTPlatform = ""
)

type JWTPayload struct {
	Tenant          *string `json:"tenant"`
	Platform        *string `json:"platform,omitempty"`
	PlatformAccount *string `json:"platformAccount,omitempty"`
	InternalToken   *string `json:"internalToken,omitempty"`
}

func GenerateJWT(payload JWTPayload) (string, error) {
	//println(tenantConfig)
	// 将payload 转成 jwt.MapClaims
	claims := jwt.MapClaims{}
	if payload.Tenant != nil && *payload.Tenant != "" {
		claims["tenant"] = *payload.Tenant
	}
	if payload.Platform != nil && *payload.Platform != "" {
		claims["platform"] = *payload.Platform
	}
	if payload.PlatformAccount != nil && *payload.PlatformAccount != "" {
		claims["platformAccount"] = *payload.PlatformAccount
	}
	if payload.InternalToken != nil && *payload.InternalToken != "" {
		claims["internalToken"] = *payload.InternalToken
	}
	// 创建 token 对象
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// 签名并获取完整的编码后的 token 字符串
	secretKey := []byte(nacos.ProjectConfig.TenantConfigs[*payload.Tenant].Auth.JWTKey)
	return token.SignedString(secretKey)
}

func GenerateJWTByChannel(channel commonConfig.Channel, currentPlatform JWTPlatform) (string, error) {
	pa := ""
	switch currentPlatform {
	case JWTPlatformWhatsApp:
		pa = channel.Platforms.Whatsapp.ID
	case JWTPlatformGenesysCloud:
		pa = channel.Platforms.GenesysCloud.ID
	case JWTPlatformEmail:
		pa = channel.Platforms.Email.ID
	default:
		return "", errors.New("currentPlatform not support")
	}
	return GenerateJWT(JWTPayload{
		Tenant:          Ptr(channel.Tenant),
		Platform:        Ptr(string(currentPlatform)),
		PlatformAccount: Ptr(pa),
	})
}
func Ptr[T any](p T) *T {
	return &p
}

// JWT 中间件
func JwtMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		realIp := r.Header.Get("X-Real-IP")
		if isBlank(realIp) {
			realIp = "null"
		}
		Authorization := r.Header.Get("Authorization")
		if !strings.HasPrefix(Authorization, "Bearer ") {
			commonLogger.Error(r.Context(), "Unauthorized request : missing Authorization key in header  "+r.RequestURI+" from: "+r.Host+" realIp: "+realIp)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		tokenStr := strings.TrimPrefix(Authorization, "Bearer ")
		preResult := preParseJWTPayloadWithoutKey(tokenStr)
		if preResult == nil {
			commonLogger.Error(r.Context(), "Unauthorized : request cannot parse info"+r.RequestURI+" with invalid token from: "+r.Host+" realIp: "+realIp)
			http.Error(w, "Unauthorized", http.StatusBadRequest)
			return
		}
		if preResult.Tenant == nil {
			commonLogger.Error(r.Context(), "Unauthorized : request missing tenant info "+r.RequestURI+" with nil tenant from: "+r.Host+" realIp: "+realIp)
			http.Error(w, "Unauthorized", http.StatusBadRequest)
			return
		}
		tenant := *preResult.Tenant
		jwtToken, err := ValidateJWT(tenant, tokenStr)
		if err != nil {
			var ve *jwt.ValidationError
			if errors.As(err, &ve) {
				var errorMessage string
				switch ve.Errors {
				case jwt.ValidationErrorExpired:
					errorMessage = "Token has expired"
				case jwt.ValidationErrorNotValidYet:
					errorMessage = "Token is not yet valid"
				default:
					errorMessage = "Token is invalid."
				}
				commonLogger.Error(r.Context(), errorMessage+". Unauthorized request "+r.RequestURI+" from: "+r.Host+" realIp: "+realIp)
				http.Error(w, "Unauthorized", http.StatusUnauthorized)
				return
			}
			commonLogger.Error(r.Context(), "parse error: "+err.Error()+". Unauthorized request "+r.RequestURI+" from: "+r.Host+" realIp: "+realIp)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}

		channel, currentPlatform, err := FetchChannelByJwtToken(r, jwtToken)
		if err != nil {
			commonLogger.Error(r.Context(), "FetchChannelByJwtToken error: "+err.Error()+". Unauthorized request "+r.RequestURI+" from: "+r.Host+" realIp: "+realIp)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		if channel == nil {
			commonLogger.Error(r.Context(), "FetchChannelByJwtToken error: channel is nil. Unauthorized request "+r.RequestURI+" from: "+r.Host+" realIp: "+realIp)
			http.Error(w, "Unauthorized", http.StatusUnauthorized)
			return
		}
		r = SaveChannelConfigR(r, *channel)
		r = SaveCurrentEntryPlatformR(r, string(currentPlatform))

		// 补充header中的 trance信息 交给 logMiddleware处理
		if isBlank(r.Header.Get("traceId")) {
			r.Header.Set("traceId", uuid.New().String())
		} else {
			r.Header.Set("traceId", r.Header.Get("traceId"))
		}

		if isBlank(r.Header.Get("sourceId")) {
			r.Header.Set("sourceId", "gc_connector_system")
			if preResult.Platform != nil {
				r.Header.Set("sourceId", *preResult.Platform)
			}
		} else {
			r.Header.Set("sourceId", r.Header.Get("sourceId"))
		}

		if isBlank(r.Header.Get("previousId")) {
			r.Header.Set("previousId", "gc_connector_system")
		} else {
			r.Header.Set("previousId", r.Header.Get("previousId"))
		}
		r.Header.Set("tenant", channel.Tenant)

		next.ServeHTTP(w, r)
	})
}

func ValidateJWT(tenant, tokenStr string) (*jwt.Token, error) {
	// 解析 token
	token, err := jwt.Parse(tokenStr, func(token *jwt.Token) (interface{}, error) {
		// 确保 token 签名方法是我们预期的
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, errors.New(fmt.Sprintf("unexpected signing method: %v", token.Header["alg"]))
		}
		tenantConfig, ok := nacos.ProjectConfig.TenantConfigs[tenant]
		if !ok {
			return nil, errors.New("tenant config not found")
		}
		secretKey := []byte(tenantConfig.Auth.JWTKey)
		return secretKey, nil
	})
	return token, err
}

// 根据jwt token中获取channel
func FetchChannelByJwtToken(r *http.Request, jwtToken *jwt.Token) (*commonConfig.Channel, JWTPlatform, error) {
	// 将 jwtToken.Claims 转成JWTPayload
	claims, ok := jwtToken.Claims.(jwt.MapClaims)
	if !ok {
		return nil, JWTPlatformUndefined, errors.Wrap(errors.New("Invalid token claims"), "Failed to cast claims to jwt.MapClaims")
	}

	// 将 jwt.MapClaims 转换为 JSON 字节切片
	claimsJSON, err := json.Marshal(claims)
	if err != nil {
		return nil, JWTPlatformUndefined, errors.Wrap(err, "Failed to marshal claims to JSON")
	}

	var payload JWTPayload
	// 将 JSON 字节切片解析到 JWTPayload 结构体
	err = json.Unmarshal(claimsJSON, &payload)
	if err != nil {
		return nil, JWTPlatformUndefined, errors.Wrap(err, "Failed to unmarshal JSON to JWTPayload")
	}
	switch {
	case payload.Platform != nil && payload.PlatformAccount != nil:
		c, err := FetchChannel(*payload.Tenant, *payload.Platform, *payload.PlatformAccount)
		return c, JWTPlatform(*payload.Platform), err
	case payload.InternalToken != nil:
		err = AuthInternalToken(*payload.Tenant, *payload.InternalToken)
		if err != nil {
			return nil, JWTPlatformUndefined, err
		}
		// try to fetch platform and platformAccount
		ua := r.Header.Get("User-Agent")
		if ua == "PureCloud-Data-Actions" {
			type DA struct {
				Platform        *string `json:"platform"`
				PlatformAccount *string `json:"platformAccount"`
				Data            string  `json:"data" v:"required"`
				Tenant          *string `header:"tenant,omitempty" v:"required"`
				Payload         *struct {
					Platform        string `json:"platform" `
					PlatformAccount string `json:"platformAccount" `
				} `rawJson:"Data"`
			}
			req, passed := cthttp.Valid[DA](r)
			if passed {
				if req.Platform != nil && req.PlatformAccount != nil {
					c, err := FetchChannel(*req.Tenant, *req.Platform, *req.PlatformAccount)
					return c, JWTPlatform(*req.Platform), err
				} else {
					c, err := FetchChannel(*payload.Tenant, req.Payload.Platform, req.Payload.PlatformAccount)
					return c, JWTPlatform(req.Payload.Platform), err
				}
			} else {
				if req.Platform != nil && req.PlatformAccount != nil {
					c, err := FetchChannel(*req.Tenant, *req.Platform, *req.PlatformAccount)
					return c, JWTPlatform(*req.Platform), err
				}
			}
			return nil, JWTPlatformUndefined, errors.New("internalToken passed , but cannot find platform and platformAccount")
		} else {
			// body>param>header
			type A struct {
				Platform        *string `json:"platform" header:"platform" param:"platform" url:"platform" v:"required"`
				PlatformAccount *string `json:"platformAccount" header:"platformAccount" param:"platformAccount" url:"platformAccount" v:"required"`
			}
			req, passed := cthttp.Valid[A](r)
			if passed {
				c, err := FetchChannel(*payload.Tenant, *req.Platform, *req.PlatformAccount)
				return c, JWTPlatform(*req.Platform), err
			}
			return nil, JWTPlatformUndefined, errors.New("internalToken passed , but cannot find platform and platformAccount")
		}
	default:
		return nil, JWTPlatformUndefined, errors.New("platform and platformAccount are required")
	}
}

func preParseJWTPayloadWithoutKey(token string) *JWTPayload {
	// 分割 JWT 为三部分
	parts := strings.Split(token, ".")
	if len(parts) != 3 {
		return nil
	}

	// 获取 payload 部分
	payloadPart := parts[1]

	// 对 Base64URL 编码的 payload 进行解码
	payloadBytes, err := base64.RawURLEncoding.DecodeString(payloadPart)
	if err != nil {
		return nil
	}

	// 解析 JSON 数据
	var payload JWTPayload
	err = json.Unmarshal(payloadBytes, &payload)
	if err != nil {
		return nil
	}

	return &payload
}

// 验证internal token
func AuthInternalToken(tenant, internalToken string) error {

	if tenant == "" || internalToken == "" {
		return errors.New("tenant and internalToken cannot be empty")
	}
	// 假设这里获取到了 channel 数据
	tenantConfig := nacos.ProjectConfig.TenantConfigs[tenant]
	for _, tokenItem := range tenantConfig.Auth.Tokens {
		if tokenItem.InternalToken == internalToken && tokenItem.Enable == true {
			return nil
		}
	}
	return errors.New("internalToken not found or disable")
}

// 获取channel
func FetchChannel(tenant, platform, platformAccount string) (*commonConfig.Channel, error) {
	if platform == "" || platformAccount == "" {
		return nil, errors.New("platform and platformAccount cannot be empty")
	}
	// 假设这里获取到了 channel 数据
	if tenant == "service-level" || tenant == "" {
		for t, tc := range nacos.ProjectConfig.TenantConfigs {
			for _, channelItem := range tc.Channels {
				switch platform {
				case string(JWTPlatformWhatsApp):
					if channelItem.Platforms.Whatsapp.ID == platformAccount {
						result := channelItem
						result.Tenant = t
						return &result, nil
					}
				case string(JWTPlatformGenesysCloud):
					if channelItem.Platforms.GenesysCloud.ID == platformAccount {
						result := channelItem
						result.Tenant = t
						return &result, nil
					}
				case string(JWTPlatformEmail):
					if channelItem.Platforms.Email.ID == platformAccount {
						result := channelItem
						result.Tenant = t
						return &result, nil
					}
				default:
				}
			}
		}
	} else {
		tenantConfig := nacos.ProjectConfig.TenantConfigs[tenant]
		for _, channelItem := range tenantConfig.Channels {
			switch platform {
			case string(JWTPlatformWhatsApp):
				if channelItem.Platforms.Whatsapp.ID == platformAccount {
					result := channelItem
					result.Tenant = tenant
					return &result, nil
				}
			case string(JWTPlatformGenesysCloud):
				if channelItem.Platforms.GenesysCloud.ID == platformAccount {
					result := channelItem
					result.Tenant = tenant
					return &result, nil
				}
			case string(JWTPlatformEmail):
				if channelItem.Platforms.Email.ID == platformAccount {
					result := channelItem
					result.Tenant = tenant
					return &result, nil
				}
			default:
			}
		}
	}
	return nil, nil
}

func isBlank(s string) bool {
	return strings.TrimSpace(s) == ""
}
