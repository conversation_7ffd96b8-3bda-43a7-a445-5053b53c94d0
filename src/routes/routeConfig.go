package router

import (
	"ctint-digital-wechat-connector/src/handlers"
	"ctint-digital-wechat-connector/src/handlers/redirectHandler"
	"ctint-digital-wechat-connector/src/middleware"
	commonMiddleware "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/middleware/logMid"
	"net/http"
)

// 定义路由结构体
type route struct {
	path            string                  // 路由的路径
	method          string                  // HTTP 方法（如 GET、POST 等）
	handler         http.HandlerFunc        // 处理该路由的处理函数
	pathMiddlewares []middleware.Middleware // 路由特定的中间件
}

// 定义路由组结构体
type routeGroup struct {
	groupMiddlewares []middleware.Middleware // 路由组的中间件
	routes           []route                 // 路由组包含的路由列表
	groupName        string                  // 路由组的名称
}

var groups = []routeGroup{
	//index 打印基本信息
	//health 不打log
	{
		groupName:        "index",
		groupMiddlewares: []middleware.Middleware{},
		routes: []route{
			{
				path:            "",
				method:          http.MethodGet,
				handler:         handlers.Index,
				pathMiddlewares: []middleware.Middleware{},
			},
			{
				path:            "health",
				method:          http.MethodGet,
				handler:         handlers.Health,
				pathMiddlewares: []middleware.Middleware{},
			},
		},
	},
	{
		groupName:        "webhook",
		groupMiddlewares: []middleware.Middleware{commonMiddleware.LogMiddleware},
		routes: []route{
			{
				path:            "wechat",
				method:          http.MethodGet,
				handler:         handlers.CheckWebhook,
				pathMiddlewares: []middleware.Middleware{},
			},
			{
				path:            "wechat",
				method:          http.MethodPost,
				handler:         handlers.Index,
				pathMiddlewares: []middleware.Middleware{},
			},
		},
	},
	{
		groupName:        "redirect",
		groupMiddlewares: []middleware.Middleware{commonMiddleware.LogMiddleware},
		routes: []route{
			{
				path:            "{address}",
				method:          http.MethodGet,
				handler:         redirectHandler.RedirectGet,
				pathMiddlewares: []middleware.Middleware{},
			},
			{
				path:            "{address}",
				method:          http.MethodPost,
				handler:         redirectHandler.RedirectPost,
				pathMiddlewares: []middleware.Middleware{},
			},
		},
	},
}

var routeConfigMap = map[string][]routeGroup{
	"v1": groups,
}
