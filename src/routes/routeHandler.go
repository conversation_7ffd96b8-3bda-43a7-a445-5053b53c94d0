package router

import (
	"ctint-digital-wechat-connector/src/middleware"
	"fmt"
	"github.com/go-chi/chi/v5"
	"github.com/olekukonko/tablewriter"
	"net/http"
	"os"
)

type RouteHandler struct {
	Basepath string
}

// Init /*
//
//	配置路由组和路由 with middleWare
//
// //		Middleware顺序 先路由组后路由*/
func (routeHandler RouteHandler) Init(r *chi.Mux, routeConfigMap map[string][]routeGroup) {
	//var mountMap = map[string]*chi.Mux{}
	for version, groupConfig := range routeConfigMap {
		for _, groupRoutes := range groupConfig {
			groupName := groupRoutes.groupName
			//相当于匿名group 不会帮我们加前缀
			r.Group(func(r chi.Router) {
				//构建group范围的Middleware
				for _, groupMiddleware := range groupRoutes.groupMiddlewares {
					r.Use(groupMiddleware)
				}
				for _, routeEntity := range groupRoutes.routes {
					path := routeHandler.Basepath + "/" + "api/" + version + "/" + groupName
					if routeEntity.path != "" {
						path = path + "/" + routeEntity.path
					}
					//构建path范围的Middleware
					r.MethodFunc(routeEntity.method, path, applyMiddlewares(routeEntity.handler, routeEntity.pathMiddlewares...))
				}
			})
		}
	}
	// 打印当前所有的路径
	printRoutes(r)
	fmt.Println("init Routes End")
}

// applyMiddlewares 将中间件应用到处理函数上 并且返回已经绑定Middleware的HandlerFunc
func applyMiddlewares(handler http.HandlerFunc, middlewares ...middleware.Middleware) http.HandlerFunc {
	for i := len(middlewares) - 1; i >= 0; i-- {
		handler = middlewares[i](handler).ServeHTTP
	}
	return handler
}

func printRoutes(r chi.Router) {
	table := tablewriter.NewWriter(os.Stdout)
	table.SetHeader([]string{"Method", "Route"})
	table.SetAutoWrapText(false)
	table.SetAutoFormatHeaders(true)
	table.SetHeaderAlignment(tablewriter.ALIGN_CENTER)
	table.SetAlignment(tablewriter.ALIGN_LEFT)
	table.SetCenterSeparator("|")
	table.SetColumnSeparator("|")
	table.SetRowSeparator("-")
	table.SetHeaderLine(true)
	table.SetBorder(true)
	table.SetTablePadding("-") // pad with tabs
	table.SetNoWhiteSpace(false)

	_ = chi.Walk(r, func(method string, route string, handler http.Handler, middlewares ...func(http.Handler) http.Handler) error {
		table.Append([]string{method, route})
		return nil
	})

	table.Render()
}
