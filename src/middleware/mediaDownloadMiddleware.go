package middleware

import (
	"ctint-digital-wechat-connector/src/auth"
	"ctint-digital-wechat-connector/src/pkg/nacos"
	"net/http"

	commonConfig "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/model/config"
	"github.com/go-chi/chi/v5"
)

func MediaDownloadMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		phoneNumber := chi.URLParam(r, "phoneNumber")
		var targetChannel commonConfig.Channel

		// 遍历所有租户配置查找匹配的 phone number
		for _, tenant := range nacos.ProjectConfig.TenantConfigs {
			for _, channel := range tenant.Channels {
				if channel.Platforms.Whatsapp.ID == phoneNumber {
					targetChannel = channel
					break
				}
			}
		}
		r = auth.SaveChannelConfigR(r, targetChannel)
		next.Serve<PERSON>TP(w, r)
	})
}
