package main

import (
	"context"
	"crypto/sha1"
	"ctint-digital-wechat-connector/src/pkg/httpx"
	"ctint-digital-wechat-connector/src/pkg/nacos"
	router "ctint-digital-wechat-connector/src/routes"
	commonLogger "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/log"
	"encoding/hex"
	"fmt"
	"log"
	"net/http"
	"sort"
	"strconv"
	"strings"
)

func main() {
	//commonConfig.InitGolobalConfig()
	nacos.InitNacosProjectConfig()
	err := commonLogger.InitGlobalLogger(nacos.ProjectConfig.LogConfig, nacos.ProjectConfig.Service.Name)
	if err != nil {
		log.Fatalf("initGlobalLogger failed : %v", err)
	}
	token := "ronnie_token"
	timestamp := "1234567890"
	nonce := "abcdef123456"
	//echostr := "test_echostr"

	// 计算signature
	tmpArr := []string{token, timestamp, nonce}
	sort.Strings(tmpArr)
	tmpStr := strings.Join(tmpArr, "")
	hash := sha1.Sum([]byte(tmpStr))
	signature := hex.EncodeToString(hash[:])

	fmt.Println("正确的signature:", signature)
	httpx.Initialize(context.Background(), 500, 300, 90)
	port := strconv.Itoa(nacos.ProjectConfig.Service.Port)
	commonLogger.Info(context.Background(), "Starting server on port "+port)
	http.ListenAndServe(":"+port, router.Router())
}
