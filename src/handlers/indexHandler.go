package handlers

import (
	"context"
	"crypto/sha1"
	"ctint-digital-wechat-connector/src/models"
	utils "ctint-digital-wechat-connector/src/pkg"
	"ctint-digital-wechat-connector/src/pkg/nacos"
	"encoding/hex"
	"fmt"
	"net/http"
	"sort"
	"strings"

	commonLogger "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/log"
)

func Index(w http.ResponseWriter, r *http.Request) {
	commonLogger.Info(r.Context(), "digital api index request========")
	commonLogger.Info(context.Background(), fmt.Sprintf("日志cores: %s", commonLogger.GetCurrentCores()))
	_result := models.CommonResponse{
		Data:      "Digital Index",
		IsSuccess: true,
		Error:     "",
	}

	utils.SetResponse(w, http.StatusOK, utils.ObjectToBytes(_result))
}

func Health(w http.ResponseWriter, r *http.Request) {
	_result := models.CommonResponse{
		Data:      "Digital API Health Check",
		IsSuccess: true,
		Error:     "",
	}

	utils.SetResponse(w, http.StatusOK, utils.ObjectToBytes(_result))
}

func CheckWebhook(w http.ResponseWriter, r *http.Request) {
	commonLogger.Info(r.Context(), "check webhook request========")

	// 从请求参数中获取微信签名验证所需的参数
	signature := r.URL.Query().Get("signature")
	timestamp := r.URL.Query().Get("timestamp")
	nonce := r.URL.Query().Get("nonce")
	echostr := r.URL.Query().Get("echostr")

	// 调用微信签名检查方法
	result := wxSignatureCheck(signature, timestamp, nonce, echostr)

	commonLogger.Info(r.Context(), fmt.Sprintf("result: %s", result))
	if result != "" {
		commonLogger.Info(r.Context(), "微信签名校验通过")
		utils.SetResponse(w, http.StatusOK, []byte(result))
	} else {
		commonLogger.Info(r.Context(), "微信签名校验失败")
		utils.SetResponse(w, http.StatusOK, []byte("签名校验失败")) // 返回错误信息
	}
}

// 微信签名检查方法
func wxSignatureCheck(signature, timestamp, nonce, echostr string) string {
	// 将token、timestamp、nonce三个参数进行字典序排序
	tmpArr := []string{nacos.ProjectConfig.WechatConfig.WechatToken, timestamp, nonce}
	sort.Strings(tmpArr)

	// 将三个参数字符串拼接成一个字符串进行sha1加密
	tmpStr := strings.Join(tmpArr, "")
	hash := sha1.Sum([]byte(tmpStr))
	mytoken := hex.EncodeToString(hash[:])

	commonLogger.Info(context.Background(), fmt.Sprintf("mytoken: %s, signature: %s", mytoken, signature))
	// 将加密后的字符串与signature进行对比
	if mytoken == signature {
		commonLogger.Info(context.Background(), "签名校验通过。")
		return echostr // 如果检验成功输出echostr
	} else {
		commonLogger.Info(context.Background(), "签名校验失败。")
		return "" // 校验失败返回空字符串
	}
}
