package redirectHandler

import (
	vxDTO "ctint-digital-wechat-connector/src/models/dto/vx"
	utils "ctint-digital-wechat-connector/src/pkg"
	"ctint-digital-wechat-connector/src/pkg/cthttp"
	"ctint-digital-wechat-connector/src/pkg/nacos"
	commonLogger "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/log"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strings"
	"time"
)

func RedirectGet(w http.ResponseWriter, r *http.Request) {
	vxDto, isPassed := cthttp.Valid[vxDTO.VxCheckRequest](r, w)
	if isPassed == false {
		return
	}

	// 直接使用验证后的address参数
	if vxDto.Address == nil || *vxDto.Address == "" {
		utils.SetResponse(w, http.StatusBadRequest, utils.ObjectToBytes("Invalid address parameter"))
		return
	}
	//address 作为key
	address := *vxDto.Address
	checkingConfigMap := nacos.ProjectConfig.CheckingConfig
	if checkingConfigMap == nil {
		utils.SetResponse(w, http.StatusInternalServerError, utils.ObjectToBytes("CheckingConfig error"))
		return
	}
	currentCheckingConfig := checkingConfigMap[address]

	// 构造目标URL
	targetURL, err := buildTargetURL(currentCheckingConfig.Domain, currentCheckingConfig.Path, r.URL.RawQuery)
	if err != nil {
		utils.SetResponse(w, http.StatusBadRequest, utils.ObjectToBytes("Invalid target URL"))
		return
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建新的请求
	req, err := http.NewRequest("GET", targetURL, nil)
	if err != nil {
		utils.SetResponse(w, http.StatusInternalServerError, utils.ObjectToBytes("Failed to create request"))
		return
	}

	// 复制原始请求的headers（排除一些不应该转发的headers）
	copyHeaders(r.Header, req.Header)

	req.Header.Set("ngrok-skip-browser-warning", "true")
	// 打印完整的URL和headers信息
	fmt.Printf("=== GET Request Debug Info ===")
	commonLogger.Info(r.Context(), fmt.Sprintf("Target URL: %s", targetURL))
	commonLogger.Info(r.Context(), "Request Headers:")
	for name, values := range req.Header {
		for _, value := range values {
			commonLogger.Info(r.Context(), fmt.Sprintf("  %s: %s", name, value))
		}
	}
	// 发送请求到目标服务
	resp, err := client.Do(req)
	if err != nil {
		utils.SetResponse(w, http.StatusBadGateway, utils.ObjectToBytes("Failed to connect to target service"))
		return
	}
	defer resp.Body.Close()

	// 复制响应headers到客户端
	copyResponseHeaders(resp.Header, w.Header())

	// 设置状态码
	w.WriteHeader(resp.StatusCode)

	// 复制响应body到客户端
	_, err = io.Copy(w, resp.Body)
	if err != nil {
		// 此时已经开始写响应了，无法改变状态码，只能记录错误
		commonLogger.Error(r.Context(), fmt.Sprintf("Error copying response body: %v", err))
	}
}

func RedirectPost(w http.ResponseWriter, r *http.Request) {
	vxDto, isPassed := cthttp.Valid[vxDTO.VxCheckRequest](r, w)
	if isPassed == false {
		return
	}

	// 直接使用验证后的address参数
	if vxDto.Address == nil || *vxDto.Address == "" {
		utils.SetResponse(w, http.StatusBadRequest, utils.ObjectToBytes("Invalid address parameter"))
		return
	}
	//address 作为key
	address := *vxDto.Address
	checkingConfigMap := nacos.ProjectConfig.CheckingConfig
	if checkingConfigMap == nil {
		utils.SetResponse(w, http.StatusInternalServerError, utils.ObjectToBytes("CheckingConfig error"))
		return
	}
	currentCheckingConfig := checkingConfigMap[address]

	// 构造目标URL
	targetURL, err := buildTargetURL(currentCheckingConfig.Domain, currentCheckingConfig.Path, r.URL.RawQuery)
	if err != nil {
		utils.SetResponse(w, http.StatusBadRequest, utils.ObjectToBytes("Invalid target URL"))
		return
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	// 创建新的请求，复制请求body
	req, err := http.NewRequest("POST", targetURL, r.Body)
	if err != nil {
		utils.SetResponse(w, http.StatusInternalServerError, utils.ObjectToBytes("Failed to create request"))
		return
	}

	// 复制原始请求的headers
	copyHeaders(r.Header, req.Header)

	req.Header.Set("ngrok-skip-browser-warning", "true")

	// 打印完整的URL和headers信息
	fmt.Printf("=== POST Request Debug Info ===\n")
	fmt.Printf("Target URL: %s\n", targetURL)
	fmt.Printf("Request Headers:\n")
	for name, values := range req.Header {
		for _, value := range values {
			fmt.Printf("  %s: %s\n", name, value)
		}
	}
	fmt.Printf("================================\n")

	// 发送请求到目标服务
	resp, err := client.Do(req)
	if err != nil {
		utils.SetResponse(w, http.StatusBadGateway, utils.ObjectToBytes("Failed to connect to target service"))
		return
	}
	defer resp.Body.Close()

	// 复制响应headers到客户端
	copyResponseHeaders(resp.Header, w.Header())

	// 设置状态码
	w.WriteHeader(resp.StatusCode)

	// 复制响应body到客户端
	_, err = io.Copy(w, resp.Body)
	if err != nil {
		fmt.Printf("Error copying response body: %v\n", err)
	}
}

// 构造目标URL，可选择性地添加微信验证参数
func buildTargetURL(address, path, rawQuery string) (string, error) {
	// 如果address是完整的URL
	if strings.HasPrefix(address, "http://") || strings.HasPrefix(address, "https://") {
		targetURL := address + path
		if rawQuery != "" {
			if strings.Contains(targetURL, "?") {
				targetURL += "&" + rawQuery
			} else {
				targetURL += "?" + rawQuery
			}
		}
		return targetURL, nil
	}

	// 如果address是服务名或IP，需要构造完整URL
	baseURL := fmt.Sprintf("https://%s", address)
	baseURL += path
	if rawQuery != "" {
		baseURL += "?" + rawQuery
	}

	// 验证URL格式
	_, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}

	return baseURL, nil
}

// 复制请求headers，排除一些不应该转发的headers
func copyHeaders(src, dst http.Header) {
	skipHeaders := map[string]bool{
		"Connection":          true,
		"Keep-Alive":          true,
		"Proxy-Authenticate":  true,
		"Proxy-Authorization": true,
		"Te":                  true,
		"Trailers":            true,
		"Transfer-Encoding":   true,
		"Upgrade":             true,
	}

	for name, values := range src {
		if !skipHeaders[name] {
			for _, value := range values {
				dst.Add(name, value)
			}
		}
	}
}

// 复制响应headers
func copyResponseHeaders(src, dst http.Header) {
	skipHeaders := map[string]bool{
		"Connection":        true,
		"Keep-Alive":        true,
		"Transfer-Encoding": true,
		"Upgrade":           true,
	}

	for name, values := range src {
		if !skipHeaders[name] {
			for _, value := range values {
				dst.Add(name, value)
			}
		}
	}
}
