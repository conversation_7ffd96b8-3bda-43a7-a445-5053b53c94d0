package httpClient

import (
	"bytes"
	"context"
	"crypto/tls"
	"crypto/x509"
	"ctint-digital-wechat-connector/src/auth"
	"ctint-digital-wechat-connector/src/pkg/httpx"
	"ctint-digital-wechat-connector/src/pkg/nacos"
	commonLogger "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/log"
	commonStorage "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/storage"
	"fmt"
	"github.com/go-resty/resty/v2"
	"github.com/pkg/errors"
	"io"
	"net/http"
	"os"
)

func HeadersWithContext(ctx context.Context) (map[string]string, error) {
	headers := make(map[string]string)
	currentChannelConfig := auth.GetChannelConfig(ctx)
	if currentChannelConfig == nil {
		//记录error日志，不报错
		commonLogger.Error(ctx, "get currentChannelConfig failed when setting headers")
	}
	logConfig, ok := commonStorage.GetLogConfig(ctx)
	if !ok || logConfig == nil {
		commonLogger.Error(ctx, "Failed to retrieve log configuration from storage")
		// 设置默认配置或执行其他逻辑
	} else {
		if traceID, exists := (*logConfig)["traceId"]; exists {
			headers["traceId"] = traceID
		}
		if sourceID, exists := (*logConfig)["sourceId"]; exists {
			headers["sourceId"] = sourceID
		}
		if tenant, exists := (*logConfig)["tenant"]; exists {
			headers["tenant"] = tenant
		}
		headers["previousId"] = nacos.ProjectConfig.Service.Name
	}
	channel := auth.GetChannelConfig(ctx)
	currentChannel := auth.GetCurrentEntryPlatform(ctx)
	if channel != nil && currentChannel != nil {
		authorization, err := auth.GenerateJWTByChannel(*channel, auth.JWTPlatform(*currentChannel))
		if err != nil {
			//commonLogger.Error(ctx, fmt.Sprintf("GenerateJWT for next platform failed: %v", err))
			return nil, err
		}
		if authorization != "" {
			//commonLogger.Info(ctx, fmt.Sprintf("GenerateJWT for next platform :%s.", authorization))
			headers["Authorization"] = "Bearer " + authorization
		}
	}
	return headers, nil
}
func DefaultHttpRequest(ctx context.Context, serviceGroup, method, url string, body interface{}, headers map[string]string, result *interface{}) error {
	req := httpx.Client().NewGroupRequest(serviceGroup, method, url).WithBody(body)
	if ctx != nil {
		req = req.WithContext(ctx)
	}
	if method == http.MethodPost {
		req = req.WithHeader("Content-Type", "application/json")
	}
	if ctxHeaders, err := HeadersWithContext(ctx); err == nil {
		req = req.WithHeaders(ctxHeaders)
	} else {
		return err
	}
	if headers != nil && len(headers) > 0 {
		req = req.WithHeaders(headers)
	}
	err := req.DoJSON(result)
	if err != nil {
		return err
	}
	return nil
}
func Dialog360HttpHandler(ctx context.Context, serviceGroup, method, url string, body interface{}) (*http.Response, error) {
	channel := auth.GetChannelConfig(ctx)
	if channel == nil || channel.Platforms.Whatsapp.APIKey == "" {
		return nil, errors.New("apiKey is empty")
	}
	req := httpx.Client().NewGroupRequest(serviceGroup, method, url).WithBody(body).WithHeader("D360-API-KEY", channel.Platforms.Whatsapp.APIKey)
	if ctx != nil {
		req = req.WithContext(ctx)
	}
	if method == http.MethodPost {
		req = req.WithHeader("Content-Type", "application/json")
	}
	resp, err := req.Do()
	if err != nil {
		commonLogger.Error(ctx, fmt.Sprintf("request failed: %s, cause: %s", url, err.Error()))
		return resp, err
	}

	//if resp.StatusCode < 200 || resp.StatusCode >= 300 {
	//	errMsg := fmt.Sprintf("request failed with status code: %d, url: %s", resp.StatusCode, url)
	//	commonLogger.Error(ctx, errMsg)
	//	return resp, fmt.Errorf(errMsg) // 返回包含状态码的错误
	//}

	// Log request and response if applicable
	//LogResponse(ctx, resp, method, url)

	return resp, nil
}

// Dialog360UploadFileHttpHandler 用于文件上传
func Dialog360UploadFileHttpHandler(ctx context.Context, url string, fileName string, fileReader io.Reader) (*resty.Response, error) {
	currentChannelConfig := auth.GetChannelConfig(ctx)
	if currentChannelConfig == nil {
		//记录error
		commonLogger.Error(ctx, "get currentChannelConfig failed")
		return nil, errors.New("get currentChannelConfig failed")
	}
	dialog360Transport, err := init360TLS()
	if err != nil {
		return nil, errors.Wrap(err, "360dialog TLS error : ")
	}

	// Create a new resty client with the custom TLS config
	client := resty.New().
		SetTLSClientConfig(dialog360Transport.TLSClientConfig)

	resp, err := client.R().
		SetHeader("D360-API-KEY", currentChannelConfig.Platforms.Whatsapp.APIKey).
		SetFormData(map[string]string{
			"messaging_product": "whatsapp",
		}).
		SetFileReader("file", fileName, fileReader).
		Post(url)

	if err != nil {
		return nil, errors.Wrap(err, "upload media to 360dialog error when making request : ")
	}
	return resp, nil
}

func init360TLS() (*http.Transport, error) {
	//env := "dev" // Default to dev if not specified
	//if len(os.Args) > 1 {
	//	env = os.Args[1]
	//}
	_crt := fmt.Sprintf("../../certs/360dialog-root.crt")
	caCert, err := os.ReadFile(_crt)
	if err != nil {
		return nil, errors.Wrap(err, "init360TLS ReadFile failed.")
	}

	// 创建 CertPool
	caCertPool := x509.NewCertPool()
	caCertPool.AppendCertsFromPEM(caCert)

	// 自定义 TLS 配置
	tlsConfig := &tls.Config{
		RootCAs: caCertPool,
	}

	return &http.Transport{
		TLSClientConfig: tlsConfig,
	}, nil
}

func LogRequest(ctx context.Context, req *http.Request, method, url string, body []byte) {
	commonLogger.Info(ctx, fmt.Sprintf("\nRequest:%s. %s, Body: %s", method, url, string(body)))
}

func LogResponse(ctx context.Context, resp *http.Response, method, url string) {
	respBody, _ := io.ReadAll(resp.Body)
	resp.Body.Close()
	resp.Body = io.NopCloser(bytes.NewBuffer(respBody))
	commonLogger.Info(ctx, fmt.Sprintf("\nResponse:%s. %s, responseBody:%s", method, url, string(respBody)))
}
