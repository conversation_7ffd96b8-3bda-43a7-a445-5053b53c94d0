package nacos

import (
	commonNacos "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/nacos"
	"encoding/json"
	"fmt"
	"log"
	"sync"
)

var ProjectConfig NacosProjectConfig
var mu sync.RWMutex

// InitNacosProjectConfig 初始化并注册回调
func InitNacosProjectConfig() {
	// 定义回调函数：配置更新时do something
	commonNacos.InitNacosProjectConfig(&ProjectConfig, nil)
}

// // 更新配置的函数 (redis client)
func updateConfig(data string) {
	mu.Lock() // 使用写锁保护配置更新
	defer mu.Unlock()

	// 将 JSON 配置解析并更新全局变量
	var newConfig NacosProjectConfig
	err := json.Unmarshal([]byte(data), &newConfig)
	if err != nil {
		log.Printf("解析配置失败: %v", err)
	} else {
		ProjectConfig = newConfig
		fmt.Printf("Nacos=====================当前配置已更新\n")
	}

}
