package nacos

import commonConfig "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/model/config"

type NacosProjectConfig struct {
	Env            string                     `yaml:"env"`
	Service        Service                    `yaml:"service"`
	JWTAuth        JWTAuth                    `yaml:"jwtAuth"`
	LogConfig      commonConfig.LogConfig     `yaml:"logger"`
	Services       commonConfig.Services      `yaml:"services"`
	TenantConfigs  commonConfig.TenantConfigs `yaml:"tenantConfigs"`
	Storage        commonConfig.Storage       `yaml:"storage"`
	CheckingConfig map[string]CheckingConfig  `yaml:"webhookChecking"`
	WechatConfig   WechatConfig               `yaml:"wechatConfig"`
}

type Service struct {
	Name     string `yaml:"name"`
	BasePath string `yaml:"basePath"`
	Port     int    `yaml:"port"`
	Version  string `yaml:"version"`
}

type JWTAuth struct {
	Switch string `yaml:"switch"`
	JWTKey string `yaml:"jwtKey"`
}

type CheckingConfig struct {
	Domain string `yaml:"domain"`
	Path   string `yaml:"path"`
}
type WechatConfig struct {
	WechatToken string `yaml:"wechatToken"`
	AppId       string `yaml:"appId"`
	AppSecret   string `yaml:"appSecret"`
}
