package httpx

import (
	"net/url"
	"strings"
)

// JoinURL 安全地连接URL的基础部分和路径部分。
// 它会处理所有的边缘情况，确保生成符合标准的URL。
//
// 参数:
//   - baseURL: URL的基础部分，如 "http://example.com"
//   - paths: 一个或多个要连接的路径部分
//
// 返回:
//   - 连接后的完整URL
//   - 如果解析baseURL出错，则返回错误
func JoinURL(baseURL string, paths ...string) string {
	// 解析基础URL
	base, err := url.Parse(baseURL)
	if err != nil {
		return ""
	}

	// 确保base.Path不以斜杠结尾，除非它只是"/"
	if base.Path != "/" && strings.HasSuffix(base.Path, "/") {
		base.Path = strings.TrimSuffix(base.Path, "/")
	}

	// 处理路径部分
	var pathElements []string

	// 如果基础URL有路径且不是根路径，添加到路径元素中
	if base.Path != "" && base.Path != "/" {
		pathElements = append(pathElements, strings.TrimPrefix(base.Path, "/"))
	}

	// 处理每个额外的路径部分
	for _, p := range paths {
		// 跳过空路径
		if p == "" {
			continue
		}

		// 去除前后斜杠
		p = strings.Trim(p, "/")
		if p != "" {
			pathElements = append(pathElements, p)
		}
	}

	// 重建URL
	var result string

	// 保留协议、主机和端口
	if base.Scheme != "" {
		result = base.Scheme + "://" + base.Host
	} else {
		result = base.Host
	}

	// 添加路径部分
	if len(pathElements) > 0 {
		result += "/" + strings.Join(pathElements, "/")
	} else if base.Path == "/" {
		// 如果基础URL的路径是根路径且没有额外路径，保留这个根路径
		result += "/"
	}

	// 添加查询参数和片段（如果有）
	if base.RawQuery != "" {
		result += "?" + base.RawQuery
	}
	if base.Fragment != "" {
		result += "#" + base.Fragment
	}

	return result
}
