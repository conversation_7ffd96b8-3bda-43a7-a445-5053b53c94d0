package httpx

import (
	"context"
	"crypto/tls"
	commonLogger "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/log"
	"dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/httpx"
	httpxc "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/httpx/client"
	"fmt"
	"sync"
	"time"
)

var (
	clientManager *httpxc.ClientManager
	once          sync.Once
)

// 创建一个适配器函数，将 commonLogger.Info 的签名适配为 log.Printf
func loggerAdapter(format string, args ...interface{}) {
	msg := fmt.Sprintf(format, args...)
	commonLogger.Info(context.Background(), msg)
}

func Client() *httpxc.ClientManager {
	once.Do(func() {
		if clientManager == nil {
			clientManager = httpxc.NewClientManager()
		}
	})
	return clientManager
}

// Initialize 初始化HTTP客户端管理器
func Initialize(ctx context.Context, maxIdleConns int, idleConnTimeout, keepAliveTimeout int) {
	once.Do(func() {
		// 创建客户端管理器
		clientManager = httpxc.NewClientManager(
			httpxc.WithPoolConfig(maxIdleConns,
				time.Duration(idleConnTimeout)*time.Second,
				time.Duration(keepAliveTimeout)*time.Second),
			httpxc.WithLogger(loggerAdapter),
			httpxc.WithSharedTLSConfig(&tls.Config{InsecureSkipVerify: true}),
		)
		commonLogger.Info(ctx, "HTTP客户端管理器初始化成功")
	})
	RegisterServices()
}

const (
	ServiceGroupDefault = "default"
	ServiceGroupMedia   = "media"
	//ServiceDigitalProxy = "digital-proxy"
	//ServiceGenesysCloud = "genesys-cloud"
	//ServiceD360         = "360dialog"
)

func RegisterServices() {
	////  注册服务 : 用于流程中call cdss 服务
	//ServiceD360C := config.DefaultConfig()
	//ServiceD360C.Timeout = 15 * time.Second
	//ServiceD360C.InsecureSkipVerify = true
	//clientManager.RegisterService(ServiceD360, nacos.ProjectConfig.Services.External.D360.Host, &ServiceD360C)
	//
	//// 注册服务 : 用于流程中启动 cdss job 服务
	//ServiceDigitalProxyC := config.DefaultConfig()
	//ServiceDigitalProxyC.Timeout = 15 * time.Second
	//ServiceDigitalProxyC.InsecureSkipVerify = true
	//clientManager.RegisterService(ServiceDigitalProxy, nacos.ProjectConfig.Services.Internal.ChatProxy.Host, &ServiceDigitalProxyC)

	defaultConfig := httpx.DefaultConfig()
	defaultConfig.Timeout = 30 * time.Second
	defaultConfig.InsecureSkipVerify = true
	clientManager.RegisterDomainGroupWithConfig(ServiceGroupDefault, nil, &defaultConfig)

	mediaConfig := httpx.DefaultConfig()
	mediaConfig.Timeout = 300 * time.Second
	mediaConfig.InsecureSkipVerify = true
	clientManager.RegisterDomainGroupWithConfig(ServiceGroupMedia, nil, &mediaConfig)
}
func RefreshServices() {
	if clientManager == nil {
		return
	}
	//updater := clientManager.GetUpdater()
	//results := updater.RefreshMultipleServices(map[string]string{
	//	ServiceD360:         nacos.ProjectConfig.Services.External.D360.Host,
	//	ServiceDigitalProxy: nacos.ProjectConfig.Services.Internal.ChatProxy.Host,
	//})
	//// 记录更新结果
	//for service, success := range results {
	//	if success {
	//		commonLogger.Info(context.Background(), fmt.Sprintf("服务 '%s' host更新成功", service))
	//	} else {
	//		commonLogger.Error(context.Background(), fmt.Sprintf("服务 '%s' host更新失败", service))
	//	}
	//}
}

// Close 关闭HTTP客户端管理器
func Close() {
	if clientManager != nil {
		// 默认客户端不需要显式释放，因为 clientManager.Close() 会处理
		clientManager.Close()
		commonLogger.Info(context.Background(), "HTTP客户端管理器已关闭")
	}
}
