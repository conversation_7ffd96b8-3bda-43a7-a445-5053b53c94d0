package cthttp

import (
	"encoding/json"
	"fmt"
	"net/http"

	commonLogger "dev.azure.com/ctint-product-development/ctint-microservices/_git/ctint-digital-common-lib.git/common/log"
	"github.com/kuah/cerror"
)

type Error struct {
	Message *string `json:"message,omitempty"`
	Code    *int    `json:"code,omitempty"`
}
type Response struct {
	Data      *interface{} `json:"data,omitempty"`
	IsSuccess bool         `json:"isSuccess"`
	Error     *Error       `json:"error,omitempty"`
	ReqId     *string      `json:"reqId,omitempty"`
}

func Succeed(w http.ResponseWriter, data interface{}, reqId *string) {
	_result := Response{
		IsSuccess: true,
	}
	if data != nil {
		switch data.(type) {
		case []byte:
			var jsonData interface{}
			if err := json.Unmarshal(data.([]byte), &jsonData); err == nil {
				_result.Data = &jsonData
			} else {
				// 如果解析失败，退回到字符串形式
				strData := string(data.([]byte))
				dataInterface := interface{}(strData)
				_result.Data = &dataInterface
			}
		default:
			_result.Data = &data
		}
	}
	if reqId != nil && *reqId != "" {
		_result.ReqId = reqId
	}
	b, err := json.Marshal(_result)
	if err != nil {
		b = []byte("")
	}
	SetResponse(w, http.StatusOK, b)
}
func Failed(w http.ResponseWriter, err error, reqId *string) {
	if err == nil {
		return
	}
	if reqId != nil && *reqId != "" {
		commonLogger.Error(nil, fmt.Sprintf("Request failed: reqId %s", *reqId))
	}
	commonLogger.Error(nil, fmt.Sprintf("Request failed: %v", err))

	responseCode := http.StatusInternalServerError
	errCode := cerror.GetCode(err)
	switch errCode {
	case http.StatusUnauthorized:
		responseCode = http.StatusUnauthorized
	case http.StatusBadRequest:
		responseCode = http.StatusBadRequest
	}
	rootErrMessage := cerror.Cause(err).Error()
	_result := Response{
		Error: &Error{
			Message: &rootErrMessage,
			Code:    &errCode,
		},
		IsSuccess: false,
	}
	if reqId != nil && *reqId != "" {
		_result.ReqId = reqId
	}
	b := ObjectToBytes(_result)
	SetResponse(w, responseCode, b)
}

func SetResponse(w http.ResponseWriter, httpStatus int, responseJson []byte) {
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(httpStatus)
	_, _ = w.Write(responseJson)
}

func ObjectToBytes(obj interface{}) []byte {
	_json, err := json.Marshal(obj)
	if err != nil {
		return []byte("")
	}

	return _json
}
