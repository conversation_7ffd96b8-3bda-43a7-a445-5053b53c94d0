package cthttp

import (
	"net/http"

	"github.com/kuah/cerror"
	"github.com/kuah/chttp"
)

// body>param>header
func Valid[T any](r *http.Request, ws ...interface{}) (T, bool) {
	req, validation, err := chttp.Valid[T](r)
	switch validation {
	case chttp.ParserResultError, chttp.ParserResultNotVerified:
		if len(ws) > 0 {
			if w, ok := ws[0].(http.ResponseWriter); ok {
				Failed(w, cerror.SetCode(err, http.StatusBadRequest), nil)
			}
		}
		return req, false
	case chttp.ParserResultSuccess:
		return req, true
	default:
		return req, false
	}
}

func ReadRequestBody[T any](r *http.Request) (*T, error) {
	return chttp.ReadRequestBody[T](r)
}
