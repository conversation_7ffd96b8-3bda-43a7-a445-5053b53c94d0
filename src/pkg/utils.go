package utils

import (
	"encoding/json"
	"net/http"
)

func SetResponse(w http.ResponseWriter, httpStatus int, responseJson []byte) {
	// 设置 Content-Type 为 application/json
	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(httpStatus)
	w.Write(responseJson)
}

func ObjectToBytes(obj interface{}) []byte {
	_json, err := json.Marshal(obj)
	if err != nil {
		return []byte("")
	}

	return _json
}

func Ptr[T any](p T) *T {
	return &p
}
