package models

import (
	"encoding/json"
	"net/http"
)

type CommonResponse struct {
	Data      interface{} `json:"data,omitempty"`
	IsSuccess bool        `json:"isSuccess"`
	Error     string      `json:"error,omitempty"`
}

type StateRequest struct {
	Key      string          `json:"key"`
	Value    json.RawMessage `json:"value"`
	Metadata json.RawMessage `json:"metadata"`
}

type RequestResult struct {
	Error    error
	Status   int
	Response *http.Response
}
