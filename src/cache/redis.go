package cache

import (
	"github.com/redis/go-redis/v9"
	"log"
)

type Redis struct {
	Options *redis.Options
	Rdb     *redis.Client
}

func (r *Redis) Connect() *redis.Client {
	r.Options = &redis.Options{
		Addr:     "digital.redis.cache.windows.net:6379",
		Password: "aLOiX6vK2idUSzVdYxNvcWAYT5sMrX6bmAzCaDOvF5I=", // 没有密码，默认值
		DB:       0,                                              // 默认DB 0
	}
	rdb := redis.NewClient(r.Options)
	r.Rdb = rdb
	return rdb
}

func (r *Redis) Close() {
	err := r.Rdb.Close()
	if err != nil {
		log.Println("closed redis client:" + err.Error())
		return
	}
}
